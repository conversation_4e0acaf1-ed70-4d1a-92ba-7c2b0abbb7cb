{"name": "ngx-drag-scroll-demo", "version": "0.0.0", "scripts": {"postinstall": "husky install", "ng": "ng", "start": "ng serve", "build": "ng build", "package": "ng build ngx-drag-scroll --configuration production && node copy-artifacts.js", "artifacts": "node copy-artifacts.js", "test": "ng test --watch=false", "lint": "npx eslint .", "lint:fix": "npx eslint . --fix", "format": "npx prettier 'src/**/*.{js,jsx,ts,tsx,html}' --write", "changelog": "test \"`git rev-parse --abbrev-ref HEAD`\" = \"master\" && conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "clean": "npm run rimraf -- node_modules", "rimraf": "<PERSON><PERSON><PERSON>"}, "author": "<PERSON>", "license": "MIT", "private": true, "dependencies": {"@angular/animations": "20.3.2", "@angular/cdk": "18.0.4", "@angular/common": "20.3.2", "@angular/compiler": "20.3.2", "@angular/core": "20.3.2", "@angular/forms": "20.3.2", "@angular/material": "18.0.4", "@angular/platform-browser": "20.3.2", "@angular/platform-browser-dynamic": "20.3.2", "@angular/router": "20.3.2", "core-js": "~3.34.0", "hammerjs": "^2.0.8", "rxjs": "~7.8.1", "tslib": "^2.6.2", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-eslint/builder": "19.4.0", "@angular-eslint/eslint-plugin": "19.4.0", "@angular-eslint/eslint-plugin-template": "19.4.0", "@angular-eslint/schematics": "19.4.0", "@angular-eslint/template-parser": "19.4.0", "@angular/build": "^20.3.3", "@angular/cli": "20.3.3", "@angular/compiler-cli": "20.3.2", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@types/hammerjs": "^2.0.45", "@types/jasmine": "~5.1.4", "@types/jasminewd2": "2.0.13", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "jasmine-core": "~5.1.1", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "lint-staged": "^15.2.0", "ng-packagr": "^20.3.0", "prettier": "^3.1.1", "ts-node": "~10.9.2", "typescript": "^5.8.3"}, "optionalDependencies": {"@nx/nx-darwin-arm64": "19.3.2", "@nx/nx-darwin-x64": "19.3.2", "@nx/nx-linux-x64-gnu": "19.3.2", "@nx/nx-win32-x64-msvc": "19.3.2"}}