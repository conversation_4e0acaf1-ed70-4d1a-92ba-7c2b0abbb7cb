{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "esModuleInterop": true, "declaration": false, "module": "esnext", "moduleResolution": "bundler", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "ES2022", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "paths": {"ngx-drag-scroll": ["projects/ngx-drag-scroll/src/public-api.ts"]}, "useDefineForClassFields": false}}