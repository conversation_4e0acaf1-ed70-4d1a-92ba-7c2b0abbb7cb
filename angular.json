{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ngx-drag-scroll-demo": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": {"base": "dist/ngx-drag-scroll-demo"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "src/main.ts"}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}}}, "serve": {"builder": "@angular/build:dev-server", "options": {"buildTarget": "ngx-drag-scroll-demo:build"}, "configurations": {"production": {"buildTarget": "ngx-drag-scroll-demo:build:production"}}}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "ngx-drag-scroll-demo:build"}}, "test": {"builder": "@angular/build:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"exclude": ["**/node_modules/**"]}}}}, "ngx-drag-scroll": {"root": "projects/ngx-drag-scroll", "sourceRoot": "projects/ngx-drag-scroll/src", "projectType": "library", "prefix": "lib", "architect": {"build": {"builder": "@angular/build:ng-packagr", "options": {"tsConfig": "projects/ngx-drag-scroll/tsconfig.lib.json", "project": "projects/ngx-drag-scroll/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/ngx-drag-scroll/tsconfig.lib.prod.json"}}}, "test": {"builder": "@angular/build:karma", "options": {"main": "projects/ngx-drag-scroll/src/test.ts", "tsConfig": "projects/ngx-drag-scroll/tsconfig.spec.json", "karmaConfig": "projects/ngx-drag-scroll/karma.conf.js"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false}, "schematics": {"@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}, "@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}