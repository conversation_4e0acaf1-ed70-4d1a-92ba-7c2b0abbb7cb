{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "../../out-tsc/lib", "moduleResolution": "bundler", "declaration": true, "sourceMap": true, "inlineSources": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "types": ["node"], "lib": ["dom", "es2018"]}, "angularCompilerOptions": {"skipTemplateCodegen": true, "strictMetadataEmit": true, "fullTemplateTypeCheck": true, "strictInjectionParameters": true, "enableResourceInlining": true}, "exclude": ["src/test.ts", "**/*.spec.ts"]}