{"name": "ngx-drag-scroll", "version": "20.0.0", "description": "Lightweight drag to scroll library for Angular", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/bfwg/ngx-drag-scroll.git"}, "keywords": ["Angular", "Carousel", "Drag", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/bfwg/ngx-drag-scroll.git"}, "homepage": "https://github.com/bfwg/ngx-drag-scroll#readme", "peerDependencies": {"@angular/common": ">=5.0.0 <21.0.0", "@angular/core": ">=5.0.0 <21.0.0"}}