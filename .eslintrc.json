{"root": true, "globals": {"document": "readonly", "window": "readonly", "console": "readonly", "Promise": "readonly", "describe": "readonly", "beforeEach": "readonly", "it": "readonly", "expect": "readonly"}, "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json"], "createDefaultProgram": true}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"@angular-eslint/component-selector": "off", "@angular-eslint/directive-selector": "off", "@angular-eslint/no-input-rename": "off", "sort-imports": ["error", {"ignoreCase": false, "ignoreDeclarationSort": false, "ignoreMemberSort": false, "memberSyntaxSortOrder": ["none", "all", "multiple", "single"], "allowSeparatedGroups": true}]}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {}}]}